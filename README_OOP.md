# Simulink配線最適化ツール - オブジェクト指向版

## 概要

このツールは、人間の配線最適化プロセスを模倣してSimulinkモデルの配線を自動的に最適化する、オブジェクト指向設計による実装です。Sequential-thinkingアプローチを維持しながら、保守性、拡張性、テスタビリティを大幅に向上させています。

## アーキテクチャ

### クラス構成

```
SimulinkWiringOptimizer (メインオーケストレーター)
├── OptimizationConfig (設定管理)
├── FileManager (ファイル操作)
├── LayoutAnalyzer (レイアウト分析)
├── WiringOptimizer (配線最適化)
├── OptimizationEvaluator (評価)
├── SimulinkLine (信号線ラッパー)
└── SimulinkBlock (ブロックラッパー)
```

### 設計原則

1. **単一責任原則**: 各クラスは明確に定義された単一の責任を持つ
2. **依存性注入**: 設定オブジェクトを通じた柔軟な構成
3. **カプセル化**: 内部実装の隠蔽と安全なインターフェース
4. **拡張性**: 新機能の追加が容易な設計
5. **テスタビリティ**: 各コンポーネントの独立テストが可能

## ファイル構成

```
SimulinkWiringOptimization/
├── SimulinkWiringOptimizer.m      # メインオーケストレーター
├── OptimizationConfig.m           # 設定管理クラス
├── FileManager.m                  # ファイル操作クラス
├── LayoutAnalyzer.m               # レイアウト分析クラス
├── WiringOptimizer.m              # 配線最適化クラス
├── OptimizationEvaluator.m        # 評価クラス
├── SimulinkLine.m                 # 信号線ラッパークラス
├── SimulinkBlock.m                # ブロックラッパークラス
├── testOptimizationOOP.m          # オブジェクト指向版テストスクリプト
├── evaluate_simulink_image.py     # AI画像評価スクリプト
├── fullCarModel.slx               # テスト用Simulinkモデル
├── optimization_images/           # 最適化結果画像
└── README_OOP.md                  # このファイル
```

## 使用方法

### 基本的な使用法

```matlab
% 1. オプティマイザーの作成
optimizer = SimulinkWiringOptimizer();

% 2. 最適化実行
result = optimizer.optimize('fullCarModel.slx');

% 3. 結果表示
optimizer.displayResults();

% 4. メトリクス取得
metrics = optimizer.getMetrics();
```

### カスタム設定での使用

```matlab
% カスタム設定でオプティマイザーを作成
optimizer = SimulinkWiringOptimizer(...
    'preserveLines', false, ...
    'useAI', true, ...
    'verbose', true, ...
    'wiringParams', struct('baseOffset', 15, 'minSpacing', 20));

% 最適化実行
result = optimizer.optimize('fullCarModel.slx');
```

### サブシステム最適化

```matlab
% 特定のサブシステムのみを最適化
optimizer = SimulinkWiringOptimizer();
result = optimizer.optimizeSubsystem('fullCarModel.slx', 'fullCarModel/Engine');
```

### 動的設定変更

```matlab
% オプティマイザー作成後の設定変更
optimizer = SimulinkWiringOptimizer();
optimizer.updateConfig('preserveLines', false, 'useAI', true);
result = optimizer.optimize('fullCarModel.slx');
```

## 主要クラスの詳細

### SimulinkWiringOptimizer

メインのオーケストレータークラス。全体の処理フローを管理し、各コンポーネントを協調させます。

**主要メソッド:**
- `optimize(modelName, options)` - メイン最適化関数
- `optimizeSubsystem(modelName, subsystemName)` - サブシステム最適化
- `getMetrics(systemName)` - メトリクス取得
- `updateConfig(options)` - 設定更新
- `displayResults()` - 結果表示

### OptimizationConfig

設定パラメータを一元管理するクラス。

**設定項目:**
- `preserveLines` - 既存線の保持
- `useAI` - AI評価の使用
- `targetSubsystem` - 対象サブシステム
- `wiringParams` - 配線パラメータ
- `outputDirectory` - 出力ディレクトリ
- `verbose` - 詳細出力モード

### LayoutAnalyzer

モデルのレイアウトを分析し、最適化に必要な情報を提供します。

**機能:**
- ブロック配置の分析
- 信号フローの特定
- レイアウト品質メトリクスの計算
- 分析結果のキャッシュ

### WiringOptimizer

実際の配線最適化を実行するクラス。

**機能:**
- 信号線の直線化
- 冗長点の除去
- 交差の最適化
- 最適化統計の収集

### OptimizationEvaluator

最適化結果の評価を行うクラス。

**機能:**
- AI評価の実行
- メトリクス計算
- 評価レポートの生成
- 評価履歴の管理

## テスト

### 基本テスト

```matlab
% 全テストの実行
testOptimizationOOP();

% 個別テストの実行
runBasicTest('fullCarModel.slx');
runAdvancedTest('fullCarModel.slx');
runSubsystemTest('fullCarModel.slx');
```

### パフォーマンステスト

```matlab
runPerformanceTest('fullCarModel.slx');
runStressTest('fullCarModel.slx');
```

## 設定パラメータ

### 基本設定

| パラメータ | 型 | デフォルト | 説明 |
|-----------|----|-----------|----- |
| `preserveLines` | logical | true | 既存の線を保持するか |
| `useAI` | logical | true | AI評価を使用するか |
| `targetSubsystem` | string | '' | 特定のサブシステムのみを最適化 |
| `verbose` | logical | true | 詳細出力モード |
| `outputDirectory` | string | 'optimization_images' | 出力ディレクトリ |

### 配線パラメータ

| パラメータ | 型 | デフォルト | 説明 |
|-----------|----|-----------|----- |
| `baseOffset` | double | 10 | 基本オフセット |
| `maxOffset` | double | 50 | 最大オフセット |
| `commonXOffset` | double | 50 | 共通Xオフセット |
| `scaleFactor` | double | 0.5 | スケールファクター |
| `minSpacing` | double | 15 | 最小間隔 |
| `tolerance` | double | 1e-6 | 許容誤差 |
| `maxIterations` | double | 100 | 最大反復回数 |

## AI評価の設定

AI評価を使用する場合は、OpenAI API キーを環境変数に設定してください：

```bash
# Windows (PowerShell)
$env:OPENAI_API_KEY = "your-api-key-here"

# Linux/Mac
export OPENAI_API_KEY="your-api-key-here"
```

## 出力ファイル

### 画像ファイル
- `optimization_images/[system]_before_[timestamp].png` - 最適化前
- `optimization_images/[system]_after_[timestamp].png` - 最適化後

### モデルファイル
- `[model]_backup.slx` - 元のモデルのバックアップ
- `[model]_optimized_[timestamp].slx` - 最適化されたモデル

## 拡張方法

### 新しい最適化アルゴリズムの追加

1. `WiringOptimizer`クラスに新しいメソッドを追加
2. 必要に応じて設定パラメータを`OptimizationConfig`に追加
3. テストケースを作成

### 新しい評価メトリクスの追加

1. `OptimizationEvaluator`クラスの`calculateMetrics`メソッドを拡張
2. レポート生成機能を更新

### 新しいファイル形式のサポート

1. `FileManager`クラスに新しいメソッドを追加
2. 必要に応じて他のクラスを更新

## トラブルシューティング

### よくある問題

1. **クラスが見つからない**
   - MATLABパスに全てのクラスファイルが含まれているか確認

2. **設定エラー**
   - `OptimizationConfig`の設定値が正しいか確認
   - 必須パラメータが不足していないか確認

3. **メモリ不足**
   - 大きなモデルの場合は`clearResults()`でキャッシュをクリア

### デバッグ

```matlab
% 詳細出力を有効にしてデバッグ
optimizer = SimulinkWiringOptimizer('verbose', true);

% エラー時のスタックトレース確認
try
    result = optimizer.optimize('model.slx');
catch ME
    fprintf('エラー: %s\n', ME.message);
    for i = 1:length(ME.stack)
        fprintf('  %s (行 %d)\n', ME.stack(i).name, ME.stack(i).line);
    end
end
```

## パフォーマンス最適化

1. **キャッシュの活用**: `LayoutAnalyzer`は分析結果をキャッシュ
2. **バッチ処理**: 複数モデルの処理時は設定を再利用
3. **メモリ管理**: 定期的な`clearResults()`の実行

## 今後の拡張予定

1. **並列処理**: 複数サブシステムの並列最適化
2. **機械学習**: 最適化パターンの学習機能
3. **GUI**: グラフィカルユーザーインターフェース
4. **プラグインシステム**: サードパーティ拡張のサポート

## ライセンス

このツールは教育・研究目的で開発されています。商用利用については別途ご相談ください。
