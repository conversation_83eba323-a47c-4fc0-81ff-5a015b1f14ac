# LayoutAnalyzer クラス設計書

## 概要

`LayoutAnalyzer`クラスは、Simulinkモデルのレイアウト構造を分析し、配線最適化に必要な情報を抽出・提供するクラスです。人間が配線を最適化する際に行う「全体のレイアウト把握」プロセスを自動化します。

## 設計思想・考え方

### 1. 人間の分析プロセスの模倣

人間がSimulinkモデルの配線を最適化する際の思考プロセス：

```
1. 全体を俯瞰 → ブロックの配置パターンを把握
2. 信号の流れを理解 → データフローの方向性を特定
3. 問題箇所の特定 → 複雑な配線や交差を発見
4. 最適化戦略の立案 → どこから手を付けるかを決定
```

`LayoutAnalyzer`は、この人間の認知プロセスをアルゴリズム化しています。

### 2. アプローチ

分析処理を段階的に実行：

- **Phase 1**: 基本情報収集（ブロック・線の列挙）
- **Phase 2**: 構造分析（位置関係・接続関係）
- **Phase 3**: パターン認識（信号フロー・レイヤー構造）
- **Phase 4**: 品質評価（メトリクス計算）

### 3. キャッシュ戦略

分析結果をキャッシュすることで：

- 同一システムの再分析を高速化
- メモリ効率の向上
- 段階的な分析の実現

## オブジェクト構成図

上記のMermaid図を参照してください。

## メソッド処理概要

### 1. メインメソッド

#### `analyzeLayout(systemName): layoutInfo`

**目的**: システム全体のレイアウト分析を実行するメインメソッド

**処理フロー**:

```
1. キャッシュチェック
   ├─ キャッシュ存在 → キャッシュから返却
   └─ キャッシュなし → 新規分析実行

2. 基本情報収集
   ├─ ブロック情報分析 (analyzeBlocks)
   ├─ 信号線情報分析 (analyzeLines)
   └─ 境界ボックス計算 (calculateBoundingBox)

3. 高次分析
   ├─ 信号フロー分析 (analyzeSignalFlow)
   └─ レイアウトメトリクス計算 (calculateLayoutMetrics)

4. 結果統合・キャッシュ保存
```

**入力**: `systemName` - 分析対象システム名
**出力**: `layoutInfo` - 包括的なレイアウト情報構造体

### 2. 基本分析メソッド

#### `analyzeBlocks(systemName): blocks[]`

**目的**: システム内の全ブロックの詳細情報を収集

**処理詳細**:

```matlab
1. find_system でブロックパス取得
2. 各ブロックに対して:
   ├─ SimulinkBlock オブジェクト作成
   ├─ getBlockInfo() で詳細情報取得
   ├─ エラーハンドリング（無効ブロックのスキップ）
   └─ 結果配列に追加
3. ブロック情報配列を返却
```

**取得情報**:

- ハンドル、名前、位置、タイプ
- 中心座標、幅、高さ
- サブシステム・リンクライブラリ判定

#### `analyzeLines(systemName): lines[]`

**目的**: システム内の全信号線の詳細情報を収集

**処理詳細**:

```matlab
1. find_system で信号線ハンドル取得
2. 各信号線に対して:
   ├─ SimulinkLine オブジェクト作成
   ├─ 点座標、接続情報取得
   ├─ 線種別判定（水平/垂直/複雑）
   ├─ 長さ・セグメント数計算
   └─ 結果配列に追加
3. 信号線情報配列を返却
```

**取得情報**:

- ハンドル、点座標、接続ブロック
- 線種別、長さ、セグメント数
- 水平/垂直判定

#### `calculateBoundingBox(blocks): boundingBox`

**目的**: ブロック群を包含する最小矩形を計算

**処理詳細**:

```matlab
1. 全ブロック位置の最小/最大値計算
   ├─ minX = min(positions(:,1))
   ├─ minY = min(positions(:,2))
   ├─ maxX = max(positions(:,3))
   └─ maxY = max(positions(:,4))
2. 境界ボックス [minX, minY, maxX, maxY] 返却
```

### 3. 高次分析メソッド

#### `analyzeSignalFlow(blocks, lines): signalFlow`

**目的**: 信号フローの方向性とレイヤー構造を分析

**処理フロー**:

```
1. 接続グラフ構築 (buildConnectionGraph)
   └─ ブロック間の接続関係をグラフ化

2. フロー方向推定 (estimateFlowDirection)
   ├─ 接続ベクトルの統計分析
   └─ 主要方向の決定

3. レイヤー特定 (identifyLayers)
   ├─ フロー方向に基づく座標クラスタリング
   └─ 階層構造の抽出
```

#### `buildConnectionGraph(blocks, lines): connections[]`

**目的**: ブロック間の接続関係をグラフ構造で表現

**処理詳細**:

```matlab
1. 各信号線に対して:
   ├─ 送信元ブロックのインデックス検索
   ├─ 送信先ブロックのインデックス検索
   ├─ 接続情報構造体作成
   └─ 接続配列に追加
2. 接続グラフを返却
```

#### `estimateFlowDirection(blocks, connections): direction`

**目的**: 主要な信号フロー方向を統計的に推定

**アルゴリズム**:

```matlab
1. 各接続の方向ベクトル計算
   └─ vector = dstCenter - srcCenter

2. 平均方向ベクトル計算
   └─ avgVector = mean(directionVectors)

3. 主要方向判定
   ├─ |avgVector.x| > |avgVector.y| → 水平方向主
   │   ├─ avgVector.x > 0 → 'left_to_right'
   │   └─ avgVector.x < 0 → 'right_to_left'
   └─ |avgVector.y| > |avgVector.x| → 垂直方向主
       ├─ avgVector.y > 0 → 'top_to_bottom'
       └─ avgVector.y < 0 → 'bottom_to_top'
```

#### `identifyLayers(blocks, connections, direction): layers[]`

**目的**: フロー方向に基づいてブロックをレイヤーに分類

**クラスタリングアルゴリズム**:

```matlab
1. 方向に応じた座標選択
   ├─ 水平フロー → X座標使用
   └─ 垂直フロー → Y座標使用

2. 座標ベースクラスタリング
   ├─ 座標をソート
   ├─ 許容値(tolerance)以内の座標をグループ化
   └─ レイヤー配列作成

3. レイヤー情報返却
```

### 4. メトリクス計算メソッド

#### `calculateLayoutMetrics(layoutInfo): metrics`

**目的**: レイアウト品質を定量化するメトリクスを計算

**計算項目**:

1. **基本メトリクス**

   - `blockCount`: ブロック数
   - `lineCount`: 信号線数
2. **配線品質メトリクス**

   ```matlab
   straightLineRatio = straightLines / totalLines * 100
   averageSegmentsPerLine = totalSegments / totalLines
   averageLineLength = totalLength / totalLines
   ```
3. **レイアウト密度**

   ```matlab
   layoutArea = (maxX - minX) * (maxY - minY)
   layoutDensity = blockCount / layoutArea
   ```

### 5. ユーティリティメソッド

#### `clearCache(): void`

- 分析キャッシュを完全クリア
- メモリ使用量の最適化

#### `getCacheInfo(): cacheInfo`

- キャッシュの状態情報を取得
- デバッグ・監視用

#### `displayLayoutSummary(layoutInfo): void`

- 分析結果のサマリーを標準出力に表示
- デバッグ・確認用

## 処理パフォーマンス特性

### キャッシュ効果

- **初回分析**: O(n + m) - n:ブロック数, m:信号線数
- **キャッシュヒット**: O(1) - 即座に結果返却

### メモリ使用量

- **ブロック情報**: 約50バイト/ブロック
- **信号線情報**: 約100バイト/線
- **キャッシュ**: システムあたり数KB〜数MB

### 計算複雑度

- **基本分析**: O(n + m)
- **信号フロー分析**: O(m²) - 接続グラフ構築
- **レイヤー分析**: O(n log n) - ソート処理

## 使用例

### 基本的な使用方法

```matlab
% 1. アナライザーの作成
config = OptimizationConfig();
analyzer = LayoutAnalyzer(config);

% 2. レイアウト分析の実行
layoutInfo = analyzer.analyzeLayout('myModel');

% 3. 結果の確認
fprintf('ブロック数: %d\n', layoutInfo.blockCount);
fprintf('信号線数: %d\n', layoutInfo.lineCount);
fprintf('直線率: %.1f%%\n', layoutInfo.metrics.straightLineRatio);
```

### 詳細分析の例

```matlab
% 個別コンポーネントの分析
blocks = analyzer.analyzeBlocks('myModel');
lines = analyzer.analyzeLines('myModel');

% 信号フロー分析
signalFlow = analyzer.analyzeSignalFlow(blocks, lines);
fprintf('主要フロー方向: %s\n', signalFlow.direction);
fprintf('レイヤー数: %d\n', length(signalFlow.layers));
```

## エラーハンドリング

### 一般的なエラーケース

1. **無効なシステム名**

   - 存在しないシステムが指定された場合
   - 空の結果を返却し、警告を出力
2. **ブロック/線の取得失敗**

   - 個別の失敗はスキップして処理継続
   - 全体の失敗時のみエラー
3. **メモリ不足**

   - キャッシュクリアによる回復試行
   - 段階的な分析への切り替え

### デバッグ支援

```matlab
% 詳細ログの有効化
config.verbose = true;

% キャッシュ状態の確認
cacheInfo = analyzer.getCacheInfo();
fprintf('キャッシュエントリ数: %d\n', cacheInfo.entryCount);

% キャッシュクリア
analyzer.clearCache();
```

## 処理フロー図

### メイン分析フロー

```mermaid
flowchart TD
    A[analyzeLayout開始] --> B{キャッシュ確認}
    B -->|存在| C[キャッシュから返却]
    B -->|なし| D[新規分析開始]

    D --> E[analyzeBlocks]
    E --> F[analyzeLines]
    F --> G[calculateBoundingBox]
    G --> H[analyzeSignalFlow]
    H --> I[calculateLayoutMetrics]
    I --> J[結果統合]
    J --> K[キャッシュ保存]
    K --> L[結果返却]

    C --> M[完了]
    L --> M
```

### 信号フロー分析フロー

```mermaid
flowchart TD
    A[analyzeSignalFlow開始] --> B[buildConnectionGraph]
    B --> C[接続関係抽出]
    C --> D[estimateFlowDirection]
    D --> E[方向ベクトル計算]
    E --> F[統計分析]
    F --> G[主要方向決定]
    G --> H[identifyLayers]
    H --> I[座標クラスタリング]
    I --> J[レイヤー構造抽出]
    J --> K[signalFlow構造体作成]
    K --> L[完了]
```

## 設計パターンと原則

### 1. Strategy Pattern (戦略パターン)

- 異なる分析アルゴリズムを切り替え可能
- フロー方向推定の複数手法対応

### 2. Cache Pattern (キャッシュパターン)

- 分析結果の効率的な再利用
- メモリとCPUのトレードオフ最適化

### 3. Template Method Pattern (テンプレートメソッドパターン)

- 分析処理の共通フローを定義
- 個別ステップの拡張性確保

## 拡張ポイント

### 1. 新しい分析アルゴリズムの追加

```matlab
% カスタム分析メソッドの追加例
function customMetrics = analyzeCustomPattern(obj, layoutInfo)
    % 独自のパターン分析ロジック
    customMetrics = struct();
    % ... 実装
end
```

### 2. 新しいメトリクスの追加

```matlab
% メトリクス計算の拡張例
function metrics = calculateLayoutMetrics(obj, layoutInfo)
    % 既存メトリクス計算
    metrics = obj.calculateBasicMetrics(layoutInfo);

    % カスタムメトリクス追加
    metrics.customScore = obj.calculateCustomScore(layoutInfo);
end
```

## まとめ

### LayoutAnalyzerの特徴

1. **人間の思考プロセスの模倣**

   - 段階的な分析アプローチ
   - 直感的な理解しやすさ
2. **高いパフォーマンス**

   - キャッシュによる高速化
   - 効率的なアルゴリズム選択
3. **拡張性**

   - モジュラー設計
   - プラグイン可能な分析手法
4. **堅牢性**

   - 包括的なエラーハンドリング
   - グレースフルデグラデーション

### 適用範囲

- **小規模モデル** (〜100ブロック): リアルタイム分析
- **中規模モデル** (100〜1000ブロック): 高速分析
- **大規模モデル** (1000ブロック〜): バッチ分析

### 今後の発展方向

1. **機械学習の統合**

   - パターン認識の自動化
   - 最適化提案の高度化
2. **並列処理の導入**

   - 大規模モデルの高速化
   - リアルタイム分析の実現
3. **可視化機能の強化**

   - インタラクティブな分析結果表示
   - 3D レイアウト分析
4. **他ツールとの連携**

   - CADツールとの統合
   - バージョン管理システムとの連携

## 参考資料

### 関連クラス

- `SimulinkBlock.m` - ブロック操作の詳細
- `SimulinkLine.m` - 信号線操作の詳細
- `OptimizationConfig.m` - 設定管理の詳細

### アルゴリズム参考文献

- グラフ理論: 接続関係の分析
- クラスタリング: レイヤー特定
- 統計分析: フロー方向推定

### 設計パターン参考文献

- GoF Design Patterns
- MATLAB OOP Best Practices
- Clean Code Principles
